# Zorgportaal Presentatie

Een professionele Reveal.js presentatie voor het Zorgportaal project.

## Overzicht

Deze presentatie toont de voordelen van het Zorgportaal systeem voor zorgorganisaties, met focus op:
- Vermindering van administratieve druk
- Verbetering van zorgkwaliteit
- Activering van zorgdoelen
- Privacy en veiligheid
- Implementatie en support

## Structuur

### Hoofdslides (1-8)
1. **Intro** - Introductie van Zorgportaal
2. **De Uitdaging** - Huidige problemen in de zorg
3. **De Oplossing** - Hoe Zorgportaal helpt
4. **Resultaten voor het Bestuur** - Concrete voordelen
5. **Privacy & Waarborgen** - Veiligheid en compliance
6. **Ontwikkelingsroadmap** - Fases van implementatie
7. **Financieel Model** - Transparante kostenstructuur
8. **<PERSON><PERSON>** - Call to action

### Annexen (A1-A4)
- **Annex 1**: Demo & Bewijs
- **Annex 2**: KPI's voor het Bestuur
- **Annex 3**: Risico's & Fallback
- **Annex 4**: Implementatie & Support

## Gebruik

### Lokaal openen
1. Open `index.html` in een moderne webbrowser
2. Gebruik de pijltjestoetsen om te navigeren
3. Druk op `ESC` voor overzicht van alle slides

### Navigatie
- **Pijltjestoetsen**: Navigeren tussen slides
- **Spatiebalk**: Volgende slide
- **ESC**: Overzicht van alle slides
- **F**: Volledig scherm
- **S**: Speaker notes (indien beschikbaar)

### Online hosting
Upload alle bestanden naar een webserver of gebruik services zoals:
- GitHub Pages
- Netlify
- Vercel

## Technische Details

- **Framework**: Reveal.js 4.3.1
- **Icons**: Font Awesome 6.0.0
- **Responsive**: Werkt op desktop, tablet en mobiel
- **Browser support**: Alle moderne browsers

## Aanpassingen

### Kleuren wijzigen
Pas de CSS variabelen aan in de `<style>` sectie:
```css
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #4a90e2;
    --accent-color: #27ae60;
}
```

### Content wijzigen
Bewerk de HTML content binnen de `<section>` tags voor elke slide.

### Nieuwe slides toevoegen
Voeg nieuwe `<section>` elementen toe binnen de `<div class="slides">` container.

## Features

- **Professioneel design** met zorggerichte kleuren
- **Interactieve elementen** met hover effecten
- **Responsive layout** voor alle schermformaten
- **Toegankelijke navigatie** met keyboard support
- **Print-vriendelijk** voor handouts
- **Smooth transitions** tussen slides

## Support

Voor vragen over de presentatie of technische ondersteuning, neem contact op met het ontwikkelteam.
