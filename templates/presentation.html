<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/theme.css">
    
    <style>
        /* Critical CSS for immediate rendering */
        .reveal .slides section {
            text-align: left;
        }
        
        .reveal h1, .reveal h2, .reveal h3 {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="reveal">
        <div class="slides">
            {{slides}}
        </div>
    </div>

    <!-- Progress indicator -->
    <div class="progress-indicator">
        <div class="progress-bar"></div>
    </div>

    <!-- Navigation hints -->
    <div class="navigation-hints">
        <div class="hint">
            <i class="fas fa-arrow-left"></i>
            <i class="fas fa-arrow-right"></i>
            <span>Navigeer met pijltjestoetsen</span>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/plugin/markdown/markdown.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/plugin/highlight/highlight.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/plugin/notes/notes.js"></script>
    <script src="assets/js/presentation.js"></script>
</body>
</html>
