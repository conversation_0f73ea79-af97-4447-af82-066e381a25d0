# Zorgportaal Presentation - Markdown Workflow

Een moderne, markdown-gebaseerde workflow voor het maken en onderhouden van professionele presentaties.

## 🎯 Overzicht

Deze presentatie gebruikt een geavanceerd build systeem waarbij:
- **Elke slide** is een apart `.md` bestand voor eenvoudige bewerking
- **Automatische build** combineert alle slides tot een complete presentatie
- **Visueel prachtig** design geïnspireerd op moderne healthcare standaarden
- **Volledig responsief** voor alle apparaten

## 📁 Projectstructuur

```
presentatie_zorgverslag/
├── slides/                 # Markdown bestanden voor elke slide
│   ├── 01-intro.md
│   ├── 02-uitdaging.md
│   ├── 03-oplossing.md
│   └── ...
├── templates/              # HTML templates
│   └── presentation.html
├── assets/                 # Statische bestanden
│   ├── css/
│   │   └── theme.css      # Hoofdstijl
│   ├── js/
│   │   └── presentation.js # Interactiviteit
│   └── images/            # Afbeeldingen
├── build.js               # Build script
├── package.json           # Dependencies
└── index.html            # Gegenereerde presentatie
```

## 🚀 Snelstart

### 1. Dependencies installeren
```bash
npm install
```

### 2. Presentatie bouwen
```bash
npm run build
```

### 3. Lokaal bekijken
```bash
npm run serve
# Ga naar http://localhost:8000
```

### 4. Development mode (auto-rebuild)
```bash
npm run watch
# Wijzigingen in slides/ worden automatisch gebouwd
```

## ✏️ Slides bewerken

### Slide structuur
Elke slide heeft een frontmatter sectie en markdown content:

```markdown
---
layout: solution
title: De Oplossing
icon: lightbulb
---

# De Oplossing

## Zorgportaal pakt beide problemen tegelijk aan

### 1. Administratieve Druk Verlagen
- Medewerker spreekt verslag eenvoudig in
- AI zet dit automatisch om naar gestructureerde tekst (SOAP)
```

### Beschikbare layouts
- `intro` - Introductie slide met gradient achtergrond
- `problem` - Probleem slides met statistieken
- `solution` - Oplossing slides met feature boxes
- `results` - Resultaat slides met KPI's
- `security` - Veiligheid slides met badges
- `roadmap` - Roadmap slides met timeline
- `financial` - Financiële slides
- `cta` - Call-to-action slides
- `kpi` - KPI dashboard slides
- `annex` - Annex slides
- `default` - Standaard layout

### Frontmatter opties
```yaml
---
layout: solution          # Layout type (verplicht)
title: Slide Titel       # Titel in sidebar
icon: lightbulb          # Font Awesome icon naam
background: gradient     # Achtergrond type (optioneel)
---
```

## 🎨 Styling aanpassen

### Kleuren wijzigen
Bewerk `assets/css/theme.css` en pas de CSS variabelen aan:

```css
:root {
  --primary-orange: #FF8C42;
  --primary-orange-light: #FFB366;
  --primary-orange-dark: #FF6B35;
  --secondary-gray: #2C2C2C;
  /* ... meer variabelen */
}
```

### Nieuwe layouts toevoegen
1. Voeg layout toe aan `build.js` in de `generateSlideLayout` functie
2. Voeg bijbehorende CSS toe aan `theme.css`
3. Rebuild de presentatie

## 📱 Responsiviteit

De presentatie is volledig responsief met breakpoints:
- **Desktop** (1200px+): Volledige asymmetrische layout
- **Tablet** (768px-1200px): Aangepaste sidebar layout
- **Mobiel** (480px-768px): Gestapelde layout
- **Klein mobiel** (<480px): Gecomprimeerde layout

## 🔧 Build systeem

### Hoe het werkt
1. **Lees slides**: Alle `.md` bestanden in `slides/` worden gelezen
2. **Parse frontmatter**: YAML metadata wordt geëxtraheerd
3. **Convert markdown**: Markdown wordt omgezet naar HTML
4. **Apply layouts**: HTML wordt in de juiste layout geplaatst
5. **Generate presentation**: Finale HTML wordt gegenereerd

### Custom enhancements
Het build systeem voegt automatisch CSS klassen toe:
- `stat-item` voor statistieken
- `result-item` voor resultaten
- `phase-item` voor roadmap fases
- `kpi-item` voor KPI's

## 🎮 Interactiviteit

### Navigatie
- **Pijltjestoetsen**: Vorige/volgende slide
- **Spatiebalk**: Volgende slide
- **ESC**: Overzicht van alle slides
- **F**: Volledig scherm
- **O**: Overview mode

### Touch gestures
- **Swipe links/rechts**: Navigeren tussen slides
- **Pinch**: Zoom in/uit (in overview mode)

### Keyboard shortcuts
- `F` - Toggle fullscreen
- `O` - Toggle overview
- `ESC` - Exit overview/fullscreen

## 📊 Performance

### Optimalisaties
- **Lazy loading**: Afbeeldingen worden alleen geladen wanneer nodig
- **Preloading**: Volgende slide wordt voorgeladen
- **CSS optimalisatie**: Critical CSS inline, rest async
- **Smooth animations**: Hardware-accelerated transitions

### Browser support
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 🔄 Workflow tips

### Nieuwe slide toevoegen
1. Maak nieuw `.md` bestand in `slides/` met nummer prefix (bijv. `13-nieuwe-slide.md`)
2. Voeg frontmatter en content toe
3. Run `npm run build`

### Slide volgorde wijzigen
Hernoem bestanden met nieuwe nummers (bijv. `01-intro.md` → `02-intro.md`)

### Content bewerken
1. Bewerk het `.md` bestand
2. Run `npm run build` of gebruik `npm run watch` voor auto-rebuild

### Afbeeldingen toevoegen
1. Plaats afbeeldingen in `assets/images/`
2. Referentie in markdown: `![Alt text](assets/images/filename.jpg)`

## 🚀 Deployment

### Statische hosting
Upload alle bestanden naar:
- GitHub Pages
- Netlify
- Vercel
- AWS S3 + CloudFront

### Build voor productie
```bash
npm run build
# Upload index.html + assets/ folder
```

## 🛠️ Troubleshooting

### Build errors
- Controleer YAML syntax in frontmatter
- Zorg dat alle `.md` bestanden valid markdown bevatten
- Check console voor specifieke errors

### Styling issues
- Hard refresh browser (Ctrl+F5)
- Check CSS syntax in `theme.css`
- Verify CSS variabelen zijn correct gedefinieerd

### Performance issues
- Optimaliseer afbeeldingen (WebP formaat aanbevolen)
- Minimaliseer custom CSS
- Test op verschillende apparaten

## 📞 Support

Voor vragen over de workflow of technische problemen:
1. Check deze documentatie
2. Bekijk console errors in browser
3. Test met `npm run build` voor build errors
