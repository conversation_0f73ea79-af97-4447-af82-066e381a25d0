#!/bin/bash

# Zorgportaal Presentation Demo Script
# Demonstrates the markdown-based workflow

echo "🎯 Zorgportaal Presentation Demo"
echo "================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are available"
echo ""

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    echo ""
fi

echo "🔨 Building presentation from markdown files..."
npm run build
echo ""

echo "📊 Presentation Statistics:"
echo "- Slides: $(ls slides/*.md | wc -l)"
echo "- Templates: $(ls templates/*.html | wc -l)"
echo "- CSS files: $(ls assets/css/*.css | wc -l)"
echo "- JS files: $(ls assets/js/*.js | wc -l)"
echo ""

echo "🎨 Design Features:"
echo "- ✅ Modern healthcare-inspired design"
echo "- ✅ Asymmetric layout with orange/gray color scheme"
echo "- ✅ Fully responsive (desktop, tablet, mobile)"
echo "- ✅ Smooth animations and transitions"
echo "- ✅ Interactive navigation"
echo ""

echo "📝 Markdown Workflow:"
echo "- ✅ Each slide is a separate .md file"
echo "- ✅ Frontmatter for layout configuration"
echo "- ✅ Automatic build process"
echo "- ✅ Live reload during development"
echo ""

echo "🚀 Available Commands:"
echo "- npm run build     : Build presentation"
echo "- npm run serve     : Start local server"
echo "- npm run watch     : Watch for changes"
echo "- npm run dev       : Build + serve"
echo ""

echo "🌐 To view the presentation:"
echo "1. Run: npm run serve"
echo "2. Open: http://localhost:8000"
echo "3. Or open: file://$(pwd)/index.html"
echo ""

echo "📱 Navigation:"
echo "- Arrow keys: Navigate slides"
echo "- Spacebar: Next slide"
echo "- ESC: Overview mode"
echo "- F: Fullscreen"
echo "- Touch: Swipe on mobile"
echo ""

echo "✏️ To edit slides:"
echo "1. Edit any .md file in slides/ folder"
echo "2. Run: npm run build"
echo "3. Refresh browser"
echo ""

echo "🎯 Demo completed! Your presentation is ready."
echo "Check MARKDOWN_WORKFLOW.md for detailed documentation."
