{"name": "zorgportaal-presentation", "version": "1.0.0", "description": "Professional Zorgportaal presentation with markdown-based workflow", "main": "build.js", "scripts": {"build": "node build.js", "dev": "node build.js && python3 -m http.server 8000", "serve": "python3 -m http.server 8000", "watch": "nodemon --watch slides/ --ext md --exec 'npm run build'"}, "keywords": ["presentation", "reveal.js", "markdown", "healthcare", "zorgportaal"], "author": "Zorgportaal Team", "license": "MIT", "dependencies": {"marked": "^9.1.6", "fs-extra": "^11.1.1"}, "devDependencies": {"nodemon": "^3.0.2"}}