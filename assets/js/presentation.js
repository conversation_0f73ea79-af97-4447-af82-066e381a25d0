// Zorgportaal Presentation JavaScript
// Enhanced functionality for the presentation

document.addEventListener("DOMContentLoaded", function () {
  // Initialize Reveal.js
  Reveal.initialize({
    hash: true,
    transition: "slide",
    transitionSpeed: "default",
    backgroundTransition: "fade",
    controls: true,
    progress: false, // We have custom progress
    center: false,
    touch: true,
    loop: false,
    rtl: false,
    navigationMode: "default",
    shuffle: false,
    fragments: true,
    fragmentInURL: false,
    embedded: false,
    help: true,
    showNotes: false,
    autoPlayMedia: null,
    preloadIframes: null,
    autoSlide: 0,
    autoSlideStoppable: true,
    autoSlideMethod: "default",
    defaultTiming: null,
    mouseWheel: false,
    hideInactiveCursor: true,
    hideCursorTime: 5000,
    previewLinks: false,
    postMessage: true,
    postMessageEvents: false,
    focusBodyOnPageVisibilityChange: true,
    keyboard: true,
    overview: true,
    disableLayout: false,
    width: 1920,
    height: 1080,
    margin: 0.02,
    minScale: 0.2,
    maxScale: 2.0,
    display: "block",

    // Plugins
    plugins: [RevealMarkdown, RevealHighlight, RevealNotes],
  });

  // Custom progress indicator
  updateProgressIndicator();

  // Event listeners
  Reveal.addEventListener("slidechanged", function (event) {
    updateProgressIndicator();
    animateSlideContent(event.currentSlide);
    updateNavigationHints();
  });

  // Initialize first slide animations
  setTimeout(() => {
    animateSlideContent(Reveal.getCurrentSlide());
  }, 100);

  // Keyboard shortcuts
  document.addEventListener("keydown", function (event) {
    switch (event.key) {
      case "f":
      case "F":
        if (event.ctrlKey || event.metaKey) return;
        event.preventDefault();
        toggleFullscreen();
        break;
      case "o":
      case "O":
        if (event.ctrlKey || event.metaKey) return;
        event.preventDefault();
        Reveal.toggleOverview();
        break;
    }
  });

  // Touch gestures for mobile
  let touchStartX = 0;
  let touchStartY = 0;

  document.addEventListener("touchstart", function (event) {
    touchStartX = event.touches[0].clientX;
    touchStartY = event.touches[0].clientY;
  });

  document.addEventListener("touchend", function (event) {
    if (!touchStartX || !touchStartY) return;

    let touchEndX = event.changedTouches[0].clientX;
    let touchEndY = event.changedTouches[0].clientY;

    let deltaX = touchStartX - touchEndX;
    let deltaY = touchStartY - touchEndY;

    // Minimum swipe distance
    if (Math.abs(deltaX) < 50 && Math.abs(deltaY) < 50) return;

    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // Horizontal swipe
      if (deltaX > 0) {
        Reveal.next();
      } else {
        Reveal.prev();
      }
    }

    touchStartX = 0;
    touchStartY = 0;
  });
});

// Update custom progress indicator
function updateProgressIndicator() {
  const progressBar = document.querySelector(".progress-bar");
  if (!progressBar) return;

  const currentSlide = Reveal.getIndices().h;
  const totalSlides = Reveal.getTotalSlides();
  const progress = (currentSlide / (totalSlides - 1)) * 100;

  progressBar.style.width = `${Math.min(progress, 100)}%`;
}

// Animate slide content on entry
function animateSlideContent(slide) {
  if (!slide) return;

  // Reset animations
  const animatedElements = slide.querySelectorAll(
    ".content-card, .stat-box, .result-item, .kpi-item, .phase-item"
  );
  animatedElements.forEach((el, index) => {
    el.style.opacity = "0";
    el.style.transform = "translateY(30px)";

    setTimeout(() => {
      el.style.transition = "opacity 0.6s ease, transform 0.6s ease";
      el.style.opacity = "1";
      el.style.transform = "translateY(0)";
    }, index * 100 + 200);
  });

  // Animate icons
  const icons = slide.querySelectorAll(".icon-circle");
  icons.forEach((icon, index) => {
    icon.style.opacity = "0";
    icon.style.transform = "scale(0.8)";

    setTimeout(() => {
      icon.style.transition = "opacity 0.4s ease, transform 0.4s ease";
      icon.style.opacity = "1";
      icon.style.transform = "scale(1)";
    }, index * 150 + 100);
  });

  // Animate text elements
  const textElements = slide.querySelectorAll("h1, h2, h3, p, li");
  textElements.forEach((el, index) => {
    if (el.closest(".content-card, .stat-box, .result-item")) return; // Skip if already animated

    el.style.opacity = "0";
    el.style.transform = "translateX(-20px)";

    setTimeout(() => {
      el.style.transition = "opacity 0.5s ease, transform 0.5s ease";
      el.style.opacity = "1";
      el.style.transform = "translateX(0)";
    }, index * 50 + 300);
  });
}

// Update navigation hints based on current position
function updateNavigationHints() {
  const hints = document.querySelector(".navigation-hints .hint span");
  if (!hints) return;

  const indices = Reveal.getIndices();
  const totalSlides = Reveal.getTotalSlides();

  if (indices.h === 0) {
    hints.textContent = "Start presentatie →";
  } else if (indices.h === totalSlides - 1) {
    hints.textContent = "← Einde presentatie";
  } else {
    hints.textContent = "Navigeer met pijltjestoetsen";
  }
}

// Toggle fullscreen
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch((err) => {
      console.log(`Error attempting to enable fullscreen: ${err.message}`);
    });
  } else {
    document.exitFullscreen();
  }
}

// Utility function to create slide layouts
function createSlideLayout(type, content) {
  const layouts = {
    intro: (content) => `
            <div class="slide-container intro-slide">
                <div class="slide-sidebar">
                    <div class="icon-circle">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h1>${content.title}</h1>
                    <p>${content.subtitle}</p>
                </div>
                <div class="slide-content">
                    <h2>${content.heading}</h2>
                    <p>${content.description}</p>
                    <div class="result-item">
                        <i class="fas fa-arrow-right"></i>
                        <span>${content.benefits}</span>
                    </div>
                </div>
            </div>
        `,

    problem: (content) => `
            <div class="slide-container">
                <div class="slide-sidebar">
                    <div class="icon-circle">
                        <i class="fas fa-${content.icon}"></i>
                    </div>
                    <h1>${content.title}</h1>
                    <div class="icon-navigation">
                        ${content.stats
                          .map(
                            (stat) => `
                            <div class="icon-circle">
                                <i class="fas fa-${stat.icon}"></i>
                            </div>
                        `
                          )
                          .join("")}
                    </div>
                </div>
                <div class="slide-content">
                    <h2>${content.heading}</h2>
                    ${content.stats
                      .map(
                        (stat) => `
                        <div class="stat-box">
                            <span class="stat-number">${stat.number}</span>
                            <span>${stat.description}</span>
                        </div>
                    `
                      )
                      .join("")}
                    ${
                      content.extra
                        ? `
                        <div class="content-card">
                            <h3>${content.extra.title}</h3>
                            <p>${content.extra.description}</p>
                        </div>
                    `
                        : ""
                    }
                </div>
            </div>
        `,

    solution: (content) => `
            <div class="slide-container">
                <div class="slide-sidebar">
                    <div class="icon-circle">
                        <i class="fas fa-${content.icon}"></i>
                    </div>
                    <h1>${content.title}</h1>
                </div>
                <div class="slide-content">
                    <h2>${content.heading}</h2>
                    <p>${content.description}</p>
                    ${content.solutions
                      .map(
                        (solution) => `
                        <div class="solution-box">
                            <h3><i class="fas fa-${solution.icon}"></i> ${
                          solution.title
                        }</h3>
                            <ul>
                                ${solution.points
                                  .map((point) => `<li>${point}</li>`)
                                  .join("")}
                            </ul>
                        </div>
                    `
                      )
                      .join("")}
                    <div class="result-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>${content.conclusion}</span>
                    </div>
                </div>
            </div>
        `,
  };

  return layouts[type] ? layouts[type](content) : "";
}

// Performance optimization
function optimizePerformance() {
  // Lazy load images
  const images = document.querySelectorAll("img[data-src]");
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.removeAttribute("data-src");
        observer.unobserve(img);
      }
    });
  });

  images.forEach((img) => imageObserver.observe(img));

  // Preload next slide
  Reveal.addEventListener("slidechanged", function (event) {
    const nextSlide = event.currentSlide.nextElementSibling;
    if (nextSlide) {
      // Preload resources for next slide
      const images = nextSlide.querySelectorAll("img[data-src]");
      images.forEach((img) => {
        if (img.dataset.src) {
          const preloadImg = new Image();
          preloadImg.src = img.dataset.src;
        }
      });
    }
  });
}

// Initialize performance optimizations
document.addEventListener("DOMContentLoaded", optimizePerformance);

// Export for potential external use
window.ZorgportaalPresentation = {
  createSlideLayout,
  updateProgressIndicator,
  animateSlideContent,
  toggleFullscreen,
};
