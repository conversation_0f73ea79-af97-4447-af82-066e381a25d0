#!/usr/bin/env node

/**
 * Zorgportaal Presentation Builder
 * Converts Markdown slides to a complete Reveal.js presentation
 */

const fs = require('fs-extra');
const path = require('path');
const { marked } = require('marked');

class PresentationBuilder {
    constructor() {
        this.slidesDir = './slides';
        this.templatesDir = './templates';
        this.outputFile = './index.html';
        this.config = {
            title: 'Zorgportaal - Slim, veilig en mensgericht rapporteren',
            author: 'Zorgportaal Team',
            description: 'Professionele presentatie voor het Zorgportaal project'
        };
    }

    async build() {
        console.log('🚀 Building Zorgportaal presentation...');
        
        try {
            // Read all markdown files
            const slides = await this.readSlides();
            
            // Convert markdown to HTML
            const htmlSlides = await this.convertSlidesToHTML(slides);
            
            // Generate final presentation
            const presentation = await this.generatePresentation(htmlSlides);
            
            // Write output file
            await fs.writeFile(this.outputFile, presentation, 'utf8');
            
            console.log('✅ Presentation built successfully!');
            console.log(`📄 Output: ${this.outputFile}`);
            console.log(`📊 Slides: ${slides.length}`);
            
        } catch (error) {
            console.error('❌ Build failed:', error.message);
            process.exit(1);
        }
    }

    async readSlides() {
        const files = await fs.readdir(this.slidesDir);
        const markdownFiles = files
            .filter(file => file.endsWith('.md'))
            .sort(); // Ensure correct order

        const slides = [];
        
        for (const file of markdownFiles) {
            const filePath = path.join(this.slidesDir, file);
            const content = await fs.readFile(filePath, 'utf8');
            const { frontmatter, markdown } = this.parseFrontmatter(content);
            
            slides.push({
                filename: file,
                frontmatter,
                markdown,
                order: this.extractOrder(file)
            });
        }

        return slides.sort((a, b) => a.order - b.order);
    }

    parseFrontmatter(content) {
        const frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
        const match = content.match(frontmatterRegex);
        
        if (!match) {
            return { frontmatter: {}, markdown: content };
        }

        const frontmatterText = match[1];
        const markdown = match[2];
        
        // Simple YAML parser for frontmatter
        const frontmatter = {};
        frontmatterText.split('\n').forEach(line => {
            const [key, ...valueParts] = line.split(':');
            if (key && valueParts.length > 0) {
                frontmatter[key.trim()] = valueParts.join(':').trim().replace(/^["']|["']$/g, '');
            }
        });

        return { frontmatter, markdown };
    }

    extractOrder(filename) {
        const match = filename.match(/^(\d+)/);
        return match ? parseInt(match[1]) : 999;
    }

    async convertSlidesToHTML(slides) {
        const htmlSlides = [];

        for (const slide of slides) {
            const html = await this.convertSlideToHTML(slide);
            htmlSlides.push(html);
        }

        return htmlSlides;
    }

    async convertSlideToHTML(slide) {
        const { frontmatter, markdown } = slide;
        const layout = frontmatter.layout || 'default';
        
        // Configure marked for better HTML output
        marked.setOptions({
            breaks: true,
            gfm: true
        });

        const contentHTML = marked(markdown);
        
        // Generate slide based on layout
        return this.generateSlideLayout(layout, frontmatter, contentHTML);
    }

    generateSlideLayout(layout, frontmatter, contentHTML) {
        const layouts = {
            intro: () => `
                <section class="intro-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            ${contentHTML}
                        </div>
                        <div class="slide-content">
                            <div class="icon-navigation">
                                <div class="icon-circle"><i class="fas fa-stethoscope"></i></div>
                                <div class="icon-circle"><i class="fas fa-chart-line"></i></div>
                                <div class="icon-circle"><i class="fas fa-shield-alt"></i></div>
                                <div class="icon-circle"><i class="fas fa-users"></i></div>
                                <div class="icon-circle"><i class="fas fa-cogs"></i></div>
                            </div>
                        </div>
                    </div>
                </section>
            `,
            
            problem: () => `
                <section class="problem-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-${frontmatter.icon || 'exclamation-triangle'}"></i>
                            </div>
                            <h1>${frontmatter.title || 'Probleem'}</h1>
                        </div>
                        <div class="slide-content">
                            ${this.enhanceContentForLayout('problem', contentHTML)}
                        </div>
                    </div>
                </section>
            `,
            
            solution: () => `
                <section class="solution-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-${frontmatter.icon || 'lightbulb'}"></i>
                            </div>
                            <h1>${frontmatter.title || 'Oplossing'}</h1>
                        </div>
                        <div class="slide-content">
                            ${this.enhanceContentForLayout('solution', contentHTML)}
                        </div>
                    </div>
                </section>
            `,
            
            results: () => `
                <section class="results-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-${frontmatter.icon || 'chart-line'}"></i>
                            </div>
                            <h1>${frontmatter.title || 'Resultaten'}</h1>
                        </div>
                        <div class="slide-content">
                            ${this.enhanceContentForLayout('results', contentHTML)}
                        </div>
                    </div>
                </section>
            `,
            
            security: () => `
                <section class="security-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-${frontmatter.icon || 'shield-alt'}"></i>
                            </div>
                            <h1>${frontmatter.title || 'Veiligheid'}</h1>
                        </div>
                        <div class="slide-content">
                            ${this.enhanceContentForLayout('security', contentHTML)}
                            <div class="privacy-badges">
                                <div class="privacy-badge"><i class="fas fa-shield-alt"></i> Privacy by Design</div>
                                <div class="privacy-badge"><i class="fas fa-certificate"></i> ISO 27001</div>
                                <div class="privacy-badge"><i class="fas fa-award"></i> NEN 7510</div>
                                <div class="privacy-badge"><i class="fas fa-user-shield"></i> AVG-proof</div>
                            </div>
                        </div>
                    </div>
                </section>
            `,
            
            roadmap: () => `
                <section class="roadmap-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-${frontmatter.icon || 'road'}"></i>
                            </div>
                            <h1>${frontmatter.title || 'Roadmap'}</h1>
                        </div>
                        <div class="slide-content">
                            <div class="phase-timeline">
                                ${this.enhanceContentForLayout('roadmap', contentHTML)}
                            </div>
                        </div>
                    </div>
                </section>
            `,
            
            financial: () => `
                <section class="financial-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-${frontmatter.icon || 'euro-sign'}"></i>
                            </div>
                            <h1>${frontmatter.title || 'Financieel'}</h1>
                        </div>
                        <div class="slide-content">
                            ${this.enhanceContentForLayout('financial', contentHTML)}
                        </div>
                    </div>
                </section>
            `,
            
            cta: () => `
                <section class="cta-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-${frontmatter.icon || 'rocket'}"></i>
                            </div>
                            <h1>${frontmatter.title || 'Actie'}</h1>
                        </div>
                        <div class="slide-content">
                            <div class="cta-section">
                                ${contentHTML}
                            </div>
                        </div>
                    </div>
                </section>
            `,
            
            kpi: () => `
                <section class="kpi-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-${frontmatter.icon || 'chart-bar'}"></i>
                            </div>
                            <h1>${frontmatter.title || 'KPIs'}</h1>
                        </div>
                        <div class="slide-content">
                            ${this.enhanceContentForLayout('kpi', contentHTML)}
                        </div>
                    </div>
                </section>
            `,
            
            default: () => `
                <section class="default-slide">
                    <div class="slide-container">
                        <div class="slide-sidebar">
                            <div class="icon-circle">
                                <i class="fas fa-${frontmatter.icon || 'info'}"></i>
                            </div>
                            <h1>${frontmatter.title || 'Slide'}</h1>
                        </div>
                        <div class="slide-content">
                            ${contentHTML}
                        </div>
                    </div>
                </section>
            `
        };

        const layoutFunction = layouts[layout] || layouts.default;
        return layoutFunction();
    }

    enhanceContentForLayout(layout, contentHTML) {
        // Add CSS classes based on layout type
        const enhancements = {
            problem: (html) => html
                .replace(/<li>/g, '<li class="stat-item">')
                .replace(/<strong>([^<]+)<\/strong>/g, '<span class="stat-number">$1</span>'),
            
            solution: (html) => html
                .replace(/<h3>/g, '<h3 class="solution-title">')
                .replace(/<ul>/g, '<ul class="solution-list">'),
            
            results: (html) => html
                .replace(/<li>/g, '<li class="result-item"><i class="fas fa-check"></i>')
                .replace(/<strong>([^<]+)<\/strong>/g, '<strong class="highlight">$1</strong>'),
            
            security: (html) => html
                .replace(/<li>/g, '<li class="security-item"><i class="fas fa-lock"></i>'),
            
            roadmap: (html) => html
                .replace(/<h3>/g, '<div class="phase-item"><h3>')
                .replace(/<\/h3>/g, '</h3>')
                .replace(/<p>/g, '<p>')
                .replace(/<\/p>/g, '</p></div>'),
            
            financial: (html) => html
                .replace(/<li>/g, '<li class="financial-item"><i class="fas fa-euro-sign"></i>'),
            
            kpi: (html) => html
                .replace(/<h3>/g, '<div class="kpi-item"><h4>')
                .replace(/<\/h3>/g, '</h4>')
                .replace(/<p>/g, '<p>')
                .replace(/<\/p>/g, '</p></div>')
        };

        const enhancer = enhancements[layout];
        return enhancer ? enhancer(contentHTML) : contentHTML;
    }

    async generatePresentation(htmlSlides) {
        const template = await fs.readFile(path.join(this.templatesDir, 'presentation.html'), 'utf8');
        
        const slidesHTML = htmlSlides.join('\n\n');
        
        return template
            .replace('{{title}}', this.config.title)
            .replace('{{slides}}', slidesHTML);
    }
}

// Run the builder
if (require.main === module) {
    const builder = new PresentationBuilder();
    builder.build();
}

module.exports = PresentationBuilder;
